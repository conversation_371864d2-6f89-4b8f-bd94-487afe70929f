<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context=".ui.home.HomeFragment">

    <!-- First Row -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_tickets"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp"
        app:cardElevation="0dp"
        app:cardCornerRadius="12dp"
        app:cardBackgroundColor="@color/home_box_tickets_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/card_faqs"
        app:layout_constraintBottom_toTopOf="@+id/card_knowledge"
        app:layout_constraintDimensionRatio="1:1">

        <LinearLayout
            android:id="@+id/box_tickets"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="16dp"
            android:clickable="true"
            android:focusable="true"
            android:background="?attr/selectableItemBackground">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_tickets"
                android:layout_marginBottom="8dp"
                android:contentDescription="@string/menu_tickets" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/menu_tickets"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/home_box_text_color"
                android:gravity="center" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_faqs"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="8dp"
        android:layout_marginBottom="8dp"
        app:cardElevation="0dp"
        app:cardCornerRadius="12dp"
        app:cardBackgroundColor="@color/home_box_faqs_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toEndOf="@+id/card_tickets"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/card_events"
        app:layout_constraintDimensionRatio="1:1">

        <LinearLayout
            android:id="@+id/box_faqs"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="16dp"
            android:clickable="true"
            android:focusable="true"
            android:background="?attr/selectableItemBackground">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_faqs"
                android:layout_marginBottom="8dp"
                android:contentDescription="@string/menu_faqs" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/menu_faqs"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/home_box_text_color"
                android:gravity="center" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Second Row -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_knowledge"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginEnd="8dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        app:cardElevation="0dp"
        app:cardCornerRadius="12dp"
        app:cardBackgroundColor="@color/home_box_knowledge_bg"
        app:layout_constraintTop_toBottomOf="@+id/card_tickets"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/card_events"
        app:layout_constraintBottom_toTopOf="@+id/card_profile"
        app:layout_constraintDimensionRatio="1:1">

        <LinearLayout
            android:id="@+id/box_knowledge"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="16dp"
            android:clickable="true"
            android:focusable="true"
            android:background="?attr/selectableItemBackground">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_knowledge"
                android:layout_marginBottom="8dp"
                android:contentDescription="@string/menu_knowledges" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/menu_knowledges"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/home_box_text_color"
                android:gravity="center" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_events"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        app:cardElevation="0dp"
        app:cardCornerRadius="12dp"
        app:cardBackgroundColor="@color/home_box_events_bg"
        app:layout_constraintTop_toBottomOf="@+id/card_faqs"
        app:layout_constraintStart_toEndOf="@+id/card_knowledge"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/card_users"
        app:layout_constraintDimensionRatio="1:1">

        <LinearLayout
            android:id="@+id/box_events"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="16dp"
            android:clickable="true"
            android:focusable="true"
            android:background="?attr/selectableItemBackground">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_events"
                android:layout_marginBottom="8dp"
                android:contentDescription="@string/menu_events" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/menu_events"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/home_box_text_color"
                android:gravity="center" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Third Row -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_profile"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginEnd="8dp"
        android:layout_marginTop="8dp"
        app:cardElevation="0dp"
        app:cardCornerRadius="12dp"
        app:cardBackgroundColor="@color/home_box_profile_bg"
        app:layout_constraintTop_toBottomOf="@+id/card_knowledge"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/card_users"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1:1">

        <LinearLayout
            android:id="@+id/box_profile"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="16dp"
            android:clickable="true"
            android:focusable="true"
            android:background="?attr/selectableItemBackground">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_profile"
                android:layout_marginBottom="8dp"
                android:contentDescription="@string/menu_profile" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/menu_profile"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/home_box_text_color"
                android:gravity="center" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_users"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:visibility="gone"
        app:cardElevation="0dp"
        app:cardCornerRadius="12dp"
        app:cardBackgroundColor="@color/home_box_users_bg"
        app:layout_constraintTop_toBottomOf="@+id/card_events"
        app:layout_constraintStart_toEndOf="@+id/card_profile"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1:1">

        <LinearLayout
            android:id="@+id/box_users"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="16dp"
            android:clickable="true"
            android:focusable="true"
            android:background="?attr/selectableItemBackground">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_users"
                android:layout_marginBottom="8dp"
                android:contentDescription="@string/menu_users" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/menu_users"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/home_box_text_color"
                android:gravity="center" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

</androidx.constraintlayout.widget.ConstraintLayout>
